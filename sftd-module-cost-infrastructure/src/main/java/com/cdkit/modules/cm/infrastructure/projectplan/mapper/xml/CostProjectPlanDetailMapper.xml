<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdkit.modules.cm.infrastructure.projectplan.mapper.CostProjectPlanDetailMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  cost_project_plan_detail 
		WHERE
			 plan_id = #{mainId} 	</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="com.cdkit.modules.cm.infrastructure.projectplan.entity.CostProjectPlanDetail">
		SELECT * 
		FROM  cost_project_plan_detail
		WHERE
			 plan_id = #{mainId} 	</select>
</mapper>
