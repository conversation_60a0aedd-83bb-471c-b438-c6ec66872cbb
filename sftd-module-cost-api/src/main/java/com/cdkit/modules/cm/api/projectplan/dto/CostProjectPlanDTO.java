package com.cdkit.modules.cm.api.projectplan.dto;

import com.cdkit.common.aspect.annotation.Dict;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 项目计划DTO
 * @Author: sunhzh
 * @Date: 2025-07-18
 * @Version: V1.0
 */
@Schema(description = "项目计划DTO")
@Data
public class CostProjectPlanDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**UUID主键*/
    @Schema(description = "UUID主键")
    private String id;

    /**计划编号(JH+8位日期+3位流水)*/
    @Schema(description = "计划编号(JH+8位日期+3位流水)")
    private String planCode;

    /**计划名称*/
    @Schema(description = "计划名称")
    private String planName;

    /**关联父计划id*/
    @Excel(name = "关联父计划id", width = 15)
    @Schema(description = "关联父计划id")
    private String parentPlanId;

    /**状态(PENDING_SUBMIT/APPROVING/LOCKED)*/
    @Schema(description = "状态(PENDING_SUBMIT/APPROVING/LOCKED)")
    private String projectPlanStatus;

    /**项目编号*/
    @Schema(description = "项目编号")
    private String projectCode;

    /**项目名称*/
    @Schema(description = "项目名称")
    private String projectName;

    /**计划类型(年度计划/季度计划 ANNUAL/QUARTERLY)*/
    @Dict(dicCode = "cost_plan_type")
    @Schema(description = "计划类型(年度计划/季度计划 ANNUAL/QUARTERLY)")
    private String planType;

    /**中心*/
    @Schema(description = "中心")
    private String center;

    /**项目组*/
    @Schema(description = "项目组")
    private String projectGroup;

    /**合同模式*/
    @Schema(description = "合同模式")
    private String contractMode;

    /**合同编号*/
    @Schema(description = "合同编号")
    private String contractCode;

    /**合同名称*/
    @Schema(description = "合同名称")
    private String contractName;

    /**合同/预估收入(税后万元)*/
    @Schema(description = "合同/预估收入(税后万元)")
    private BigDecimal contractRevenue;

    /**直接成本小计*/
    @Schema(description = "直接成本小计")
    private BigDecimal directCostTotal;

    /**其他成本小计*/
    @Schema(description = "其他成本小计")
    private BigDecimal otherCostTotal;

    /**税金及附加小计*/
    @Schema(description = "税金及附加小计")
    private BigDecimal taxCostTotal;

    /**成本总计*/
    @Schema(description = "成本总计")
    private BigDecimal costTotal;

    /**项目利润(万元)*/
    @Schema(description = "项目利润(万元)")
    private BigDecimal projectProfit;

    /**利润率(%)*/
    @Schema(description = "利润率(%)")
    private BigDecimal profitMargin;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**创建人*/
    @Schema(description = "创建人")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username", ds = "master")
    private String createBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**更新人*/
    @Schema(description = "更新人")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username", ds = "master")
    private String updateBy;

    /**项目计划明细列表*/
    @Schema(description = "项目计划明细列表")
    private List<CostProjectPlanDetailDTO> detailList;

    /**直接成本明细列表*/
    @Schema(description = "直接成本明细列表")
    private List<CostDirectCostDTO> directCostList;

    /**其他成本明细列表*/
    @Schema(description = "其他成本明细列表")
    private List<CostOtherCostDTO> otherCostList;

    /**税金及附加明细列表*/
    @Schema(description = "税金及附加明细列表")
    private List<CostTaxCostDTO> taxCostList;

    /**原料明细列表*/
    @Schema(description = "原料明细列表")
    private List<CostMaterialDetailDTO> materialDetailList;

    /**子计划列表（季度计划）*/
    @Schema(description = "子计划列表（季度计划）")
    private List<CostProjectPlanDTO> childPlanList;
}
